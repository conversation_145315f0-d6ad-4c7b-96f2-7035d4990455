#ifndef _HEAD_H_
#define _HEAD_H_

#include <iostream>
#include <string>
#include <vector>
#include <thread>
#include <chrono>
#include <functional>
#include <unistd.h>
#include <cstdint>
#include <stdio.h>
#include <stdlib.h>
#include <cstring>
#include <unordered_map>
#include <fstream>
#include <sstream>
#include <stdarg.h>
#include <ifaddrs.h>
#include <arpa/inet.h>
#include <limits.h>
#include <list>
#include <memory>
#include <algorithm>
#include <time.h>
#include <stdlib.h>
#include <fcntl.h>
#include <sys/epoll.h>
#include <sys/inotify.h>
#include <map>
#include <cerrno>
#include <sys/eventfd.h>


using namespace std;

#endif // _HEAD_H_
