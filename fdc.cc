#include "head.h"


    typedef struct
    {
        uint8_t pid = 0;
        uint8_t FDCNegoResult2; // 参数配置      支持则填充FDC值，不支持填充个0x00
        uint8_t FDCNegoResult3; // 鉴权
        uint8_t FDCNegoResult4; // 预约
        uint8_t FDCNegoResult5; // 输出回路检测
        uint8_t FDCNegoResult6; // 供电模式
        uint8_t FDCNegoResult7; // 预充及能量传输
        uint8_t FDCNegoResult8; // 结束
    } FDCResultDef;        // 车辆功能协商确认结果报文 B2


    typedef struct
    {
        uint8_t pid = 1;
        uint8_t FDCSupport2[8]; // 参数配置    功能模式是否支持第1 - 8个FDC 非0x00（建议使用0xFF）:支持 0x00：不支持
        uint8_t FDCSupport3[8]; // 鉴权
        uint8_t FDCSupport4[8]; // 预约
        uint8_t FDCSupport5[8]; // 输出回路检测
        uint8_t FDCSupport6[8]; // 供电模式
        uint8_t FDCSupport7[8]; // 预充及能量传输
        uint8_t FDCSupport8[8]; // 结束
    } SeSupportFDCmsgDef;           // 充电机支持功能报文 B1

FDCResultDef st_FDCNegoResylt_D = {0};
FDCResultDef NegFDC_ = {1, 1, 1, 1, 1, 1, 1};
SeSupportFDCmsgDef st_FDCSupport_U = {0};
uint32_t thisID = 0;

bool FDC_Judgment()
{
    NegFDC_  = {0, 156, 169, 110, 7, 110, 6, 8};
    printf(
        "gun %d B2 success :%d :%d :%d :%d :%d :%d :%d\n",
        thisID, st_FDCNegoResylt_D.FDCNegoResult2,
        st_FDCNegoResylt_D.FDCNegoResult3,
        st_FDCNegoResylt_D.FDCNegoResult4,
        st_FDCNegoResylt_D.FDCNegoResult5,
        st_FDCNegoResylt_D.FDCNegoResult6,
        st_FDCNegoResylt_D.FDCNegoResult7,
        st_FDCNegoResylt_D.FDCNegoResult8);
    bool ret = false;
    uint8_t *FC = (uint8_t *)&st_FDCSupport_U.FDCSupport2;
    uint8_t *carFDC = (uint8_t *)&st_FDCNegoResylt_D.FDCNegoResult2;
    uint8_t *slefFDC = (uint8_t *)&NegFDC_.FDCNegoResult2;
    uint8_t **FDC = new uint8_t *[7];
 
    for(uint32_t i = 0; i < 7; i++) {
        FDC[i] = FC;
        FC += 8;
    }
    for(uint32_t i = 0; i < 7; i++) {
        slefFDC[i] = 0;
        for(uint32_t j = 0; j < 8; j++) {
            if(FDC[i][j] != 0) {
                if(carFDC[i] == (j + 1)){
                    slefFDC[i] = carFDC[i];
                    break;
                }
            }
        }
    }
    
    // 必须项功能协商成功 功能模块的注册
    if (NegFDC_.FDCNegoResult2 != 0 &&
        NegFDC_.FDCNegoResult5 != 0 &&
        NegFDC_.FDCNegoResult7 != 0 &&
        NegFDC_.FDCNegoResult8 != 0) {
            printf("gun %d success FunNeg Judg\n", thisID);
            ret = true;
    } else { // 必须项协商失败
        printf("gun %d error FunNeg Judg\n", thisID);
    }
    printf("gun %d Neg FDC :%d :%d :%d :%d :%d :%d :%d\n",
        thisID, NegFDC_.FDCNegoResult2, 
        NegFDC_.FDCNegoResult3,
        NegFDC_.FDCNegoResult4, 
        NegFDC_.FDCNegoResult5, 
        NegFDC_.FDCNegoResult6,
        NegFDC_.FDCNegoResult7, 
        NegFDC_.FDCNegoResult8);
    delete[] FDC;
    return ret;
}


int main(int argc, char const *argv[])
{
    st_FDCSupport_U.FDCSupport2[0] = 1;
    st_FDCSupport_U.FDCSupport3[0] = 1;
    st_FDCSupport_U.FDCSupport3[1] = 1;
    // st_FDCSupport_U.FDCSupport3[2] = 1; // !云端鉴权
    // st_FDCSupport_U.FDCSupport4[0] = 1; // !预约
    st_FDCSupport_U.FDCSupport5[0] = 1;
    // st_FDCSupport_U.FDCSupport6[0] = 1; // !供电调试
    st_FDCSupport_U.FDCSupport7[0] = 1;
    st_FDCSupport_U.FDCSupport8[0] = 1;
    st_FDCNegoResylt_D = {0, 1, 2, 0, 1, 0, 1, 1};
    FDC_Judgment();
    return 0;
}
