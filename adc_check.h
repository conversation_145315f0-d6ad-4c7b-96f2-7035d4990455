#include "head.h"

#ifndef ADC_CHECK_H
#define ADC_CHECK_H

#define AOLT_SUM  100
#define ADC_CHECK_MODE  0
#define CHECK_CFT_SIZE  8

typedef enum{
    CHECK_DIS       = 0,
    CHECK_START = 1,
    CHECK_END     = 2,
    CHECK_ERR     = 3,
    CHECK_NUM
}ADC_CheckFigDef;

typedef enum{
    P_CHECK_DIS       = 0,
    P_CHECK_START = 1,
    P_CHECK_END     = 2,
    P_CHECK_ERR     = 3,
}Power_CheckFigDef;

enum{
    VOLT_200V   = 0,
    VOLT_400V   = 1,
    VOLT_500V   = 2,
    VOLT_600V   = 3,
    VOLT_800V   = 4,
    VOLT_1000V  = 5,
    VOLT_NUM
};

enum{
    A_GUN   = 0,
    B_GUN   = 1,
    GUN_TYPE_NUM
};

enum{
    A_GUN_BAT   = 0,
    A_GUN_OUT   = 1,
    A_GUN_POS = 2,
    B_GUN_BAT   = 3,
    B_GUN_OUT   = 4,
    B_GUN_POS = 5,
    A_GUN_METER = 6,
    B_GUN_METER = 7,
    VOLT_TYPE
};

typedef struct{
    float x;
    float y;
}VoltPointDef;

typedef struct{
    int64_t         A_Vbat;
    int64_t         A_Vout;
    int64_t         A_Vpos;
    int64_t         B_Vbat;
    int64_t         B_Vout;
    int64_t         B_Vpos;
    int64_t         A_Meter;
    int64_t         B_Meter;
}VoltAverageDef;

//需求上传信息
typedef struct _CheckGunApply {
    float voltageApplied;           // 需求电压
    float currentApplied;           // 需求电流
    float voltagePTP;               // 模块PTP开机电压
    float currentPTP;               // 模块PTP开机电流
    uint32_t connectorApplied;      // 输出接触器期望状态(吸合/断开)
    uint32_t cc1State;
} CheckGunApply;

typedef struct{
    uint32_t gun;
    uint32_t bmsState;
}ChargerReviveDef;

typedef struct{
    int32_t            VarrCntBk;
    int32_t            VarrCnt;
    int32_t            Cnt;
    uint32_t         GunID;
    uint32_t         HBDelayCnt;
    uint32_t         RegisteCnt;
    uint32_t         TimeCnt1000ms;
    uint32_t         TimeCnt2000ms;
    uint32_t         TimeCnt3000ms;
    uint32_t         TimeCnt500ms;
    uint32_t         TimeCnt200ms;
    uint32_t         HBCnt;
    uint32_t         SampleCnt;
}CntDef;

typedef enum {
    //E_REGISTE_IDLE = 0,
    E_SEND_REGIDTE = 0,
    E_RECV_RESPONSE = 1,
    E_REGISTE_INIT = 2,
    E_REGISTE_SUCCESS = 3,
    E_REGISTE_FAIL
}RegisteStateDef;

class adc_check
{
    private:
        ADC_CheckFigDef  CheckFig;
        ADC_CheckFigDef  CheckCmd_;
        ADC_CheckFigDef  CheckFinishCmd_;
        Power_CheckFigDef PowerCheckCmd_;
        VoltAverageDef   Vaverage;
        VoltPointDef     point;
        CntDef           Cnt_ = {0};
        struct timespec TimeRecorder_;
        std::vector<int> socketCheck;
        std::vector<int> socketCheckRt;
        std::vector<struct sockaddr_in> sockaddrCheck;
        std::vector<struct sockaddr_in> sockaddrCheckRt;
        uint32_t thisID;
        int *pGun_;
        std::vector<RegisteStateDef> RegisteState;
    public:
        // Constructor
        adc_check() {
            // Initialize class members
            CheckFig = CHECK_START;
            CheckCmd_ = CHECK_DIS;
            CheckFinishCmd_ = CHECK_DIS;
            PowerCheckCmd_ = P_CHECK_DIS;
            thisID = 0;
            pGun_ = nullptr;

            // Initialize vectors
            RegisteState.resize(GUN_TYPE_NUM, E_REGISTE_INIT);

            // Initialize global vector
            extern std::vector<bool> g_chargerOn;
            g_chargerOn.resize(GUN_TYPE_NUM, false);
        }

        void SetCheckFig(ADC_CheckFigDef state) {CheckFig = state;};
        ADC_CheckFigDef GetCheckFig(void) {return CheckFig;};
        bool ADC_CheckContactorCmd(uint32_t gunid, int *gun);
        void CheckPorcess(void);
        bool GunApplyMsgUpdate(uint32_t voltData);
        void SendRTInfo(void);
};

#endif