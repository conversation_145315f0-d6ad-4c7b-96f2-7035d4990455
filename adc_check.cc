/* Copyright 2021, Xi'an WanMa Novel Energy Co., Ltd.
 * All rights reserved.
 *
 * Class VCIHolder and VCIGunCharging Declaration here.
 */

#include "adc_check.h"

#define DEBUG 0
#define DELAYED 50000
#define OHP_SEND_GUN_TIME        11            // 1s
#define PMM_SEND_GUN_TIME       79           // 8s

// Global variables
std::vector<bool> g_chargerOn = {false, false};
uint32_t VoltDemand[VOLT_NUM] = {200, 400, 500, 600, 800, 1000}; // Voltage demand values for each voltage level

bool adc_check::ADC_CheckContactorCmd(uint32_t gunid, int *gun)
{
    if(g_chargerOn.size() <= gunid){
        return false;
    }
    thisID = gunid;
    pGun_ = gun;
    if(g_chargerOn.at(thisID) == false){
        g_chargerOn.at(thisID) = true;
        RegisteState.at(thisID) = E_REGISTE_SUCCESS;
    }
    return g_chargerOn.at(thisID);
}

void adc_check::CheckPorcess(void)
{
    if(((RegisteState.at(A_GUN) == E_REGISTE_SUCCESS) && (RegisteState.at(B_GUN) == E_REGISTE_SUCCESS)) ||\
        (RegisteState.at(thisID) == E_REGISTE_INIT)){
        if((GunApplyMsgUpdate(VoltDemand[Cnt_.VarrCnt]) == true) || (Cnt_.VarrCntBk != Cnt_.VarrCnt)){
            SendRTInfo();
            printf("%d, %d\n", thisID, *pGun_);
        }
    }
}

bool adc_check::GunApplyMsgUpdate(uint32_t voltData)
{
    if((++Cnt_.TimeCnt500ms) % 50 != 0){
        return false;
    }
    Cnt_.VarrCntBk = Cnt_.VarrCnt;
    Cnt_.TimeCnt500ms = 0;

    if((RegisteState.at(0) == E_REGISTE_SUCCESS) && (RegisteState.at(1) == E_REGISTE_SUCCESS)){
        if(++Cnt_.GunID >= 2){
            Cnt_.GunID = 0;
        }
        thisID = Cnt_.GunID;
    }
    return true;
}

void adc_check::SendRTInfo(void)
{
    // Implementation of SendRTInfo function
    // This is a placeholder implementation
    if (thisID < RegisteState.size()) {
        // Process and send real-time information
        // For now, just update the state
        if (RegisteState[thisID] == E_REGISTE_SUCCESS) {
            // Do something with the real-time info
        }
    }
}