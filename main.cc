#include "head.h"

const uint8_t FAST[4] = {0x46, 0x41, 0x53, 0x54}; // 解放非标大电流识别码

#define	CHG_ON_ACK_CMD				(1<<1)	//启动充电,充电命令
#define	CHG_ON_ACK_VIN				(1<<2)	//VIN启动

int BigCurr() {
    const uint8_t FAST[4] = {0x46, 0x41, 0x53, 0x54}; // 解放非标大电流识别码
    uint32_t result = 0;

    // 使用 memcpy 将数组内容拷贝到 result
    std::memcpy(&result, FAST, sizeof(result));

    std::cout << "Result: 0x" << std::hex << result << std::endl;

    uint32_t result1 = 0;

    // 将每个字节移位并合并
    result1 = (static_cast<uint32_t>(FAST[0]) << 24) |
             (static_cast<uint32_t>(FAST[1]) << 16) |
             (static_cast<uint32_t>(FAST[2]) << 8)  |
             (static_cast<uint32_t>(FAST[3]));

    std::cout << "Result: 0x" << std::hex << result1 << std::endl;
    return 0;
}


// 通过 res 指针直接修改原始 vector
void processVectorWithRes(std::vector<int>* vec) {
    // 创建一个指向原始 vector 的指针
    std::vector<int>* res = vec;

    // 通过 res 指针直接修改原始 vector
    if (!res->empty()) {
        (*res)[0] = 200;  // 这会直接修改原始 vector
        (*res).at(0) = 500;
    }
}

#define DLLMINS 200 // 最小电流下降速率 0.1A/s
#define	UpLimit16(Value,UpValue)        {Value = (Value >= UpValue) ? UpValue : Value;}

typedef struct{
    int32_t         curr;
    int32_t         curr_bak;
    int32_t         DeltaI;
    uint32_t        DeltaT;
}CurrAdjustDef;


CurrAdjustDef CurrAdjust_ = {0};

bool Curr_Adjust_judge()
{
	// CurrAdjust_.curr = 200;
	if(CurrAdjust_.curr_bak != CurrAdjust_.curr) {
		CurrAdjust_.DeltaI = CurrAdjust_.curr_bak - CurrAdjust_.curr;
		if(CurrAdjust_.DeltaI > 0) {
            CurrAdjust_.DeltaT = CurrAdjust_.DeltaI / DLLMINS;
			CurrAdjust_.DeltaT += ((CurrAdjust_.DeltaI % DLLMINS) > 0);
            UpLimit16(CurrAdjust_.DeltaT, 5);
            CurrAdjust_.DeltaT *= 10; // 更换单位1s -> 100ms
            printf("DeltaT = %d\n", CurrAdjust_.DeltaT);
        }
        CurrAdjust_.curr_bak = CurrAdjust_.curr;
    }
    if(CurrAdjust_.DeltaT > 0) {
        printf("CurrAdjust_.DeltaT = %d\n", CurrAdjust_.DeltaT);
        CurrAdjust_.DeltaT--;
		return false;
    }
	return true;
}

int main() {
    for(uint32_t i = 0; i < 2; i++) {
        for(uint32_t j = 0; j < 3; j++) {
            printf("i = %d, j = %d\n", i, j);
            if(j == 1){
                // break;
            }
        }
    }
}

