#include "head.h"

typedef struct
{
    uint32_t index;
    uint32_t data1;
    uint32_t data2;
}FaultMsgDef;


const FaultMsgDef C_FaultInfo[]= {
    {0, 0x00000000, 0x00000000},
    {1, 0x00000001, 0x00000001},
    {5, 0x00000002, 0x00000002}
};

std::unordered_map<uint32_t, FaultMsgDef> faultlist;


void initizeMap(void)
{
    for (int i = 0; i < sizeof(C_FaultInfo)/sizeof(C_FaultInfo[0]); i++)
    {
        faultlist.insert(pair<uint32_t, FaultMsgDef>(C_FaultInfo[i].index, C_FaultInfo[i]));
    }
}


int main()
{
    initizeMap();
    for(auto it = faultlist.begin(); it != faultlist.end(); it++){
        std::cout << "Key: " << it->first << ", Value: " << it->second.index << std::endl;
    }
    return 0;
}