#include "head.h"

#ifndef FUNCTION_H
#define FUNCTION_H



class cahrger {

private:

};

class function_module {

public:
    function_module() {};
    virtual ~function_module() {};

    virtual uint32_t FunModProcess() = 0;

public:
    uint32_t status = 0;

};



class negotiation : public function_module {
private:
    negotiation() {};
    ~negotiation() {};
public:
    uint32_t FunModProcess();

};

class paraconfig : public function_module {
private:
    paraconfig() {};
    ~paraconfig() {};
public:
    uint32_t FunModProcess();
};


class charger {

private:
    negotiation negotiation_;
    paraconfig paraconfig_;
public:
    uint32_t chargerProcess();
    
};




#endif // FUNCTION_H
