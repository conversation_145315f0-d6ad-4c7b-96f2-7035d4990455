#include "head.h"


typedef enum {
    NON_STANDARD = 0, // 不使能
    YUTONG = 1, // 宇通非标
    LIBERATION = 2, // 解放非标
    AUTO = 9 // 自动识别
}BigCurrDef;

bool AutoBigCurrFlg_ = false; // AUTO模式下解放识别失败后置位
bool BigCurrFlg_ = false;
bool LiberationCurrFlg_ = false;
uint32_t c1 = 0;
uint32_t CURRBENCHMARK = 4000;



uint32_t YT_ACK = 0;
uint32_t lib_ACK = 0;
uint32_t NonStandHighCurrEnb = 0;

void YuTBigCurrJudge() {
    printf("recv BFC\n");
    if(YT_ACK) { // 成功
        BigCurrFlg_ = true;
        CURRBENCHMARK = 6 * 1000;
        printf("宇通 成功\n");
    } else { // 失败
        printf("宇通 失败\n");
    }
}

bool LiberationCurr() 
{
    uint32_t result = 0;
    printf("recv BRM FAST");
    if(lib_ACK) { // 成功
        LiberationCurrFlg_ = true;
        CURRBENCHMARK = 0; // 解放大电流无偏移量 只传递真实值
        printf("解放 成功\n");
        return true;
    } else { // 失败
        printf("解放 失败\n");
        return false;
    }
}


/*
/**
 * @brief 处理非标准大电流充电
 *
 * 该函数处理宇通大电流和解放大电流两种非标准充电模式，以及自动识别模式
 * - 宇通模式：需要发送CFC消息并处理BFC响应
 * - 解放模式：通过BRM消息识别
 * - 自动模式：先尝试解放模式，失败后尝试宇通模式
void NonStandardBigCurr(void)
{
    // 定义常量，提高代码可读性
    const bool isYuTongMode = (BigCurrDef::YUTONG == NonStandHighCurrEnb);
    const bool isAutoMode = (BigCurrDef::AUTO == NonStandHighCurrEnb);
    const bool isMultiGunCharging = 1; // !调试默认为真
    const bool receivedBFC = 1;
    const bool receivedBRM = 1;

    // 每25个周期发送一次大电流状态消息
    if (c1 % 25 == 0) {
        // 宇通模式下，多枪充电时发送大电流状态
        if (isYuTongMode && isMultiGunCharging) {
            // Dat_SendMsgToBMS(INDEX_CHARGER_BIGCURR_STATE); // 发送CFC非标大电流消息
            printf("send CFC\n");
        }
        // 自动模式下，解放识别失败
        else if (isAutoMode && isMultiGunCharging && AutoBigCurrFlg_) {
            // Dat_SendMsgToBMS(INDEX_CHARGER_BIGCURR_STATE); // 发送CFC非标大电流消息
            printf("send CFC\n");
        }
    }

    // 处理BFC响应消息
    if (receivedBFC && isMultiGunCharging) {
        // 宇通模式下处理BFC响应
        if (isYuTongMode) {
            YuTBigCurrJudge();
        }
        // 自动模式且已确认为宇通协议时处理BFC响应
        else if (isAutoMode && AutoBigCurrFlg_) {
            YuTBigCurrJudge();
        }
    }

    // 大电流不使能则不等待
    if (NonStandHighCurrEnb == 0) {
        BigCurrFlg_ = true;
        printf("不进行等待\n");
    }

    // 超时处理：计数超过100时，无论如何都设置大电流标志为真
    if (c1 >= 100) {
        BigCurrFlg_ = true;
        printf("超时 退出\n");
    }

    // 解放大电流识别
    if (receivedBRM && !AutoBigCurrFlg_) {
        // 尝试解放大电流识别，成功则设置大电流标志
        int32_t adck = LiberationCurr();
        if (adck == 1) {
            BigCurrFlg_ = true;
            printf("解放识别成功\n");
        }
        // 解放识别失败，设置自动识别标志，后续将尝试宇通模式
        else if(adck == -1 && isAutoMode) {
            AutoBigCurrFlg_ = true;
            printf("解放识别错误 进行宇通\n");
        } else if(adck == -1) {
            printf("解放识别错误\n");
            BigCurrFlg_ = true;

        }
    }
}
*/




void bigcurr()
{
    const bool isMultiGunCharging = 1; // !调试默认为真
    const bool receivedBFC = 1;
    const bool receivedBRM = 1;
    switch(NonStandHighCurrEnb) 
    {
        case BigCurrDef::NON_STANDARD:
            BigCurrFlg_ = true;
            printf("不使能大电流 直接退\n");
            break;
        case BigCurrDef::YUTONG:
            if (c1 % 25 == 0) {
                // 宇通模式下，多枪充电时发送大电流状态
                if(isMultiGunCharging) {
                    printf("send CFC\n");
                }
            }

            if (receivedBFC && isMultiGunCharging) {
                YuTBigCurrJudge();
            }
            break;
        case BigCurrDef::LIBERATION:
            if (receivedBRM) {
                LiberationCurr();
                BigCurrFlg_ = true;
            }
            break;
        case BigCurrDef::AUTO:
            if (receivedBRM && !AutoBigCurrFlg_) {
                if(LiberationCurr()) {
                    BigCurrFlg_ = true;
                } else {
                    AutoBigCurrFlg_ = true; // 切换非标
                    printf("切换 宇通\n");
                }
            } else if(AutoBigCurrFlg_) {
                if (c1 % 25 == 0) {
                    // 宇通模式下，多枪充电时发送大电流状态
                    if(isMultiGunCharging) {
                        printf("send CFC\n");
                    }
                }

                if (receivedBFC && isMultiGunCharging) {
                    YuTBigCurrJudge();
                }
            }
            break;
        default: break;
    }
    if(c1 >= 100) {
        BigCurrFlg_ = true;
        printf("超时退出\n");
    }
}

int main(int argc, char const *argv[])
{
    while(1) {
        BigCurrFlg_ = false;
        c1 = 0;
        CURRBENCHMARK = 4000;
        AutoBigCurrFlg_ = false;
        std::cout << "非标大电流 0:不使能 1:宇通 2:解放 9:AUTO" << std::endl;
        std::cin >> NonStandHighCurrEnb;
        std::cout << "宇通协议是否成功 0:失败 1:成功" << std::endl;
        std::cin >> YT_ACK;
        std::cout << "解放协议是否成功 0:失败 1:成功" << std::endl;
        std::cin >> lib_ACK;
        printf("-----------------------\n\b");
        while(1) {
            if(c1 % 25 == 0) {
                printf("send CRM\n");
                if(BigCurrDef::LIBERATION == NonStandHighCurrEnb || \
                BigCurrDef::AUTO == NonStandHighCurrEnb) { // 解放非标大电流
                    printf("send 解放\n");
                }
                // NonStandardBigCurr();
                bigcurr();
                if(BigCurrFlg_) {
                    printf(" CURRBENCHMARK %d \n ", CURRBENCHMARK);
                    break;
                }
            }
            c1++;
            usleep(10000);
        }
    }
    return 0;
}
