#include "head.h"
#include "adc_check.h"

#if 1
int main(int argc, char const *argv[])
{
    adc_check ADCheck;
    int startCmd[2] = {1, 0};
    int a = 0;
    while(1) {
        for(uint32_t i = 0; i < 2; i++) {
            if(startCmd[i] == 1)
            {
                // ADC check
                if((ADCheck.GetCheckFig() != CHECK_END) &&
                    (ADCheck.GetCheckFig() != CHECK_ERR)){
                    int gun_id = i; // Convert uint32_t to int for the function call
                    if(ADCheck.ADC_CheckContactorCmd(i, &gun_id) == true){
                        ADCheck.CheckPorcess();
                    }
                }
            }
        }
        usleep(50000); // 50ms = 50,000 microsecondss
    }
    return 0;
}
#endif
